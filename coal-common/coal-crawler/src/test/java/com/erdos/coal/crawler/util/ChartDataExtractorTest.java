package com.erdos.coal.crawler.util;

import com.erdos.coal.crawler.enums.IndexType;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * ChartDataExtractor 测试类
 * 用于验证优化后的数据提取功能
 */
public class ChartDataExtractorTest {

    private static final Logger logger = LoggerFactory.getLogger(ChartDataExtractorTest.class);

    @Test
    public void testShenhuaDataExtraction() {
        logger.info("开始测试神华外购数据提取...");
        
        try {
            Map<String, Map<String, String>> result = ChartDataExtractor.extractMeiyibaoData(IndexType.SHENHUA);
            
            logger.info("神华外购数据提取结果:");
            printTestResult(result, "神华外购");
            
            // 验证数据格式
            validateShenhuaData(result);
            
        } catch (Exception e) {
            logger.error("神华外购数据提取测试失败: {}", e.getMessage(), e);
        }
    }

    @Test
    public void testCCIDataExtraction() {
        logger.info("开始测试CCI指数数据提取...");
        
        try {
            Map<String, Map<String, String>> result = ChartDataExtractor.extractMeiyibaoData(IndexType.CCI);
            
            logger.info("CCI指数数据提取结果:");
            printTestResult(result, "CCI指数");
            
            // 验证数据格式
            validateStandardData(result, "CCI");
            
        } catch (Exception e) {
            logger.error("CCI指数数据提取测试失败: {}", e.getMessage(), e);
        }
    }

    @Test
    public void testCCTDDataExtraction() {
        logger.info("开始测试CCTD指数数据提取...");
        
        try {
            Map<String, Map<String, String>> result = ChartDataExtractor.extractMeiyibaoData(IndexType.CCTD);
            
            logger.info("CCTD指数数据提取结果:");
            printTestResult(result, "CCTD指数");
            
            // 验证数据格式
            validateStandardData(result, "CCTD");
            
        } catch (Exception e) {
            logger.error("CCTD指数数据提取测试失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 打印测试结果
     */
    private void printTestResult(Map<String, Map<String, String>> data, String indexName) {
        if (data.isEmpty()) {
            logger.warn("{}: 未提取到数据", indexName);
            return;
        }

        logger.info("{} 数据:", indexName);
        for (Map.Entry<String, Map<String, String>> dateEntry : data.entrySet()) {
            String date = dateEntry.getKey();
            Map<String, String> dayData = dateEntry.getValue();

            StringBuilder sb = new StringBuilder();
            sb.append(date).append(": ");

            boolean first = true;
            for (Map.Entry<String, String> priceEntry : dayData.entrySet()) {
                if (!first) sb.append(", ");
                sb.append(priceEntry.getKey()).append("=").append(priceEntry.getValue());
                first = false;
            }

            logger.info(sb.toString());
        }
        logger.info("共 {} 天的数据", data.size());
    }

    /**
     * 验证神华外购数据格式
     */
    private void validateShenhuaData(Map<String, Map<String, String>> data) {
        logger.info("验证神华外购数据格式...");
        
        for (Map.Entry<String, Map<String, String>> dateEntry : data.entrySet()) {
            String date = dateEntry.getKey();
            Map<String, String> dayData = dateEntry.getValue();
            
            // 检查日期格式
            if (!date.matches("\\d{2}-\\d{2}")) {
                logger.warn("日期格式不正确: {}", date);
            }
            
            // 检查是否包含期望的热值类型
            boolean hasExpectedCalorific = false;
            for (String calorific : dayData.keySet()) {
                if (calorific.contains("外购5500kCal") || calorific.contains("外购5000kCal") ||
                    calorific.contains("外购4500kCal") || calorific.contains("外购神优2")) {
                    hasExpectedCalorific = true;
                    break;
                }
            }
            
            if (!hasExpectedCalorific) {
                logger.warn("日期 {} 缺少期望的神华外购热值类型", date);
            }
            
            // 检查价格范围
            for (Map.Entry<String, String> priceEntry : dayData.entrySet()) {
                String price = priceEntry.getValue();
                try {
                    int priceValue = Integer.parseInt(price.replaceAll("[^0-9]", ""));
                    if (priceValue < 150 || priceValue > 1200) {
                        logger.warn("价格超出合理范围: {} = {}", priceEntry.getKey(), price);
                    }
                } catch (NumberFormatException e) {
                    logger.warn("价格格式错误: {} = {}", priceEntry.getKey(), price);
                }
            }
        }
        
        logger.info("神华外购数据格式验证完成");
    }

    /**
     * 验证标准数据格式（CCI/CCTD）
     */
    private void validateStandardData(Map<String, Map<String, String>> data, String indexType) {
        logger.info("验证{}数据格式...", indexType);
        
        for (Map.Entry<String, Map<String, String>> dateEntry : data.entrySet()) {
            String date = dateEntry.getKey();
            Map<String, String> dayData = dateEntry.getValue();
            
            // 检查日期格式
            if (!date.matches("\\d{2}-\\d{2}")) {
                logger.warn("日期格式不正确: {}", date);
            }
            
            // 检查是否包含期望的热值类型
            boolean hasExpectedCalorific = false;
            for (String calorific : dayData.keySet()) {
                if (calorific.contains("5500kCal") || calorific.contains("5000kCal") ||
                    calorific.contains("4500kCal")) {
                    hasExpectedCalorific = true;
                    break;
                }
            }
            
            if (!hasExpectedCalorific) {
                logger.warn("日期 {} 缺少期望的{}热值类型", date, indexType);
            }
            
            // 检查价格范围
            for (Map.Entry<String, String> priceEntry : dayData.entrySet()) {
                String price = priceEntry.getValue();
                try {
                    int priceValue = Integer.parseInt(price.replaceAll("[^0-9]", ""));
                    if (priceValue < 150 || priceValue > 1200) {
                        logger.warn("价格超出合理范围: {} = {}", priceEntry.getKey(), price);
                    }
                } catch (NumberFormatException e) {
                    logger.warn("价格格式错误: {} = {}", priceEntry.getKey(), price);
                }
            }
        }
        
        logger.info("{}数据格式验证完成", indexType);
    }

    /**
     * 运行所有测试
     */
    public static void main(String[] args) {
        ChartDataExtractorTest test = new ChartDataExtractorTest();
        
        logger.info("=== 开始运行ChartDataExtractor优化测试 ===");
        
        test.testShenhuaDataExtraction();
        test.testCCIDataExtraction();
        test.testCCTDDataExtraction();
        
        logger.info("=== 测试完成 ===");
    }
}
