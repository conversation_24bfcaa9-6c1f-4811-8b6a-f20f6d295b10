package com.erdos.coal.crawler.test;

import com.erdos.coal.crawler.dto.CoalIndexDataDto;
import com.erdos.coal.crawler.enums.IndexType;
import com.erdos.coal.crawler.util.ChartDataExtractor;

import java.util.List;
import java.util.Map;

/**
 * ChartDataExtractor测试类
 * 用于测试煤易宝网站数据提取功能
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
public class ChartDataExtractorTest {

    public static void main(String[] args) {
        System.out.println("=== 煤易宝网站数据提取测试 ===");
        System.out.println("开始测试ChartDataExtractor类...");

        try {
            // 测试神华外购数据提取
            testShenhuaExtraction();

            // 测试CCI指数数据提取
            testCCIExtraction();

            // 测试CCTD指数数据提取
            testCCTDExtraction();

            // 测试兼容性方法
            testCompatibilityMethods();

        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("\n=== 测试完成 ===");
    }

    /**
     * 测试神华外购数据提取
     */
    private static void testShenhuaExtraction() {
        System.out.println("\n--- 测试神华外购数据提取 ---");

        try {
            Map<String, Map<String, String>> shenhuaData = ChartDataExtractor.extractMeiyibaoData(IndexType.SHENHUA);

            if (shenhuaData.isEmpty()) {
                System.out.println("神华外购: 未提取到数据");
            } else {
                System.out.println("神华外购数据提取成功:");
                printFormattedData(shenhuaData, "神华外购");

                // 验证数据格式
                validateShenhuaData(shenhuaData);
            }

        } catch (Exception e) {
            System.err.println("神华外购数据提取失败: " + e.getMessage());
        }
    }

    /**
     * 测试CCI指数数据提取
     */
    private static void testCCIExtraction() {
        System.out.println("\n--- 测试CCI指数数据提取 ---");

        try {
            Map<String, Map<String, String>> cciData = ChartDataExtractor.extractMeiyibaoData(IndexType.CCI);

            if (cciData.isEmpty()) {
                System.out.println("CCI指数: 未提取到数据");
            } else {
                System.out.println("CCI指数数据提取成功:");
                printFormattedData(cciData, "CCI指数");

                // 验证数据格式
                validateStandardData(cciData, "CCI");
            }

        } catch (Exception e) {
            System.err.println("CCI指数数据提取失败: " + e.getMessage());
        }
    }

    /**
     * 测试CCTD指数数据提取
     */
    private static void testCCTDExtraction() {
        System.out.println("\n--- 测试CCTD指数数据提取 ---");

        try {
            Map<String, Map<String, String>> cctdData = ChartDataExtractor.extractMeiyibaoData(IndexType.CCTD);

            if (cctdData.isEmpty()) {
                System.out.println("CCTD指数: 未提取到数据");
            } else {
                System.out.println("CCTD指数数据提取成功:");
                printFormattedData(cctdData, "CCTD指数");

                // 验证数据格式
                validateStandardData(cctdData, "CCTD");
            }

        } catch (Exception e) {
            System.err.println("CCTD指数数据提取失败: " + e.getMessage());
        }
    }

    /**
     * 测试兼容性方法
     */
    private static void testCompatibilityMethods() {
        System.out.println("\n--- 测试兼容性方法 ---");

        try {
            // 测试返回CoalIndexDataDto列表的方法
            List<CoalIndexDataDto> shenhuaList = ChartDataExtractor.extractChartData(IndexType.SHENHUA);
            System.out.println("神华外购DTO列表: " + shenhuaList.size() + " 条记录");

            List<CoalIndexDataDto> cciList = ChartDataExtractor.extractChartData(IndexType.CCI);
            System.out.println("CCI指数DTO列表: " + cciList.size() + " 条记录");

            List<CoalIndexDataDto> cctdList = ChartDataExtractor.extractChartData(IndexType.CCTD);
            System.out.println("CCTD指数DTO列表: " + cctdList.size() + " 条记录");

            // 打印部分DTO数据示例
            if (!shenhuaList.isEmpty()) {
                System.out.println("神华外购DTO示例: " + shenhuaList.get(0));
            }

        } catch (Exception e) {
            System.err.println("兼容性方法测试失败: " + e.getMessage());
        }
    }

    /**
     * 打印格式化的数据
     */
    private static void printFormattedData(Map<String, Map<String, String>> data, String indexName) {
        System.out.println(indexName + " 数据格式:");

        int count = 0;
        for (Map.Entry<String, Map<String, String>> dateEntry : data.entrySet()) {
            if (count >= 3) { // 只显示前3天的数据作为示例
                System.out.println("... (共 " + data.size() + " 天的数据)");
                break;
            }

            String date = dateEntry.getKey();
            Map<String, String> dayData = dateEntry.getValue();

            StringBuilder sb = new StringBuilder();
            sb.append(date).append(": ");

            boolean first = true;
            for (Map.Entry<String, String> priceEntry : dayData.entrySet()) {
                if (!first) sb.append(", ");
                sb.append(priceEntry.getKey()).append("=").append(priceEntry.getValue());
                first = false;
            }

            System.out.println(sb.toString());
            count++;
        }
    }

    /**
     * 验证神华外购数据格式
     */
    private static void validateShenhuaData(Map<String, Map<String, String>> data) {
        System.out.println("验证神华外购数据格式...");

        for (Map.Entry<String, Map<String, String>> dateEntry : data.entrySet()) {
            String date = dateEntry.getKey();
            Map<String, String> dayData = dateEntry.getValue();

            // 检查日期格式
            if (!date.matches("\\d{2}-\\d{2}")) {
                System.out.println("警告: 日期格式不正确: " + date);
            }

            // 检查神华外购特有的热值类型
            for (String calorific : dayData.keySet()) {
                if (!calorific.startsWith("外购")) {
                    System.out.println("警告: 神华外购数据应以'外购'开头: " + calorific);
                }
            }
        }

        System.out.println("神华外购数据格式验证完成");
    }

    /**
     * 验证标准数据格式（CCI/CCTD）
     */
    private static void validateStandardData(Map<String, Map<String, String>> data, String indexType) {
        System.out.println("验证" + indexType + "数据格式...");

        for (Map.Entry<String, Map<String, String>> dateEntry : data.entrySet()) {
            String date = dateEntry.getKey();
            Map<String, String> dayData = dateEntry.getValue();

            // 检查日期格式
            if (!date.matches("\\d{2}-\\d{2}")) {
                System.out.println("警告: 日期格式不正确: " + date);
            }

            // 检查标准热值类型
            for (String calorific : dayData.keySet()) {
                if (!calorific.matches("\\d{4,5}kCal")) {
                    System.out.println("警告: " + indexType + "数据热值格式不正确: " + calorific);
                }
            }
        }

        System.out.println(indexType + "数据格式验证完成");
    }
}
