package com.erdos.coal.crawler.util;

import com.erdos.coal.crawler.dto.CoalIndexDataDto;
import com.erdos.coal.crawler.enums.IndexType;
import com.microsoft.playwright.*;
import com.microsoft.playwright.options.WaitUntilState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 煤易宝网站数据爬取工具类
 * 专门用于爬取https://www.meiyibao.com/网页的煤价指数数据
 * 包括神华外购、CCI指数、CCTD指数折线图中的每个日期对应的煤价
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public class CrawlerData {
    
    private static final Logger logger = LoggerFactory.getLogger(CrawlerData.class);
    private static final String BASE_URL = "https://www.meiyibao.com/";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("MM-dd");
    
    // 煤炭价格合理范围
    private static final BigDecimal MIN_PRICE = new BigDecimal("300");
    private static final BigDecimal MAX_PRICE = new BigDecimal("800");
    
    // 常见热值
    private static final Integer[] COMMON_CALORIFIC_VALUES = {5500, 5000, 4500};
    
    /**
     * 爬取所有类型的煤价指数数据
     * @return 包含所有指数类型数据的Map
     */
    public static Map<IndexType, List<CoalIndexDataDto>> crawlAllCoalIndexData() {
        Map<IndexType, List<CoalIndexDataDto>> resultMap = new HashMap<>();
        
        logger.info("开始爬取煤易宝网站所有煤价指数数据...");
        
        // 爬取神华外购数据
        List<CoalIndexDataDto> shenhuaData = crawlCoalIndexData(IndexType.SHENHUA);
        resultMap.put(IndexType.SHENHUA, shenhuaData);
        logger.info("神华外购数据爬取完成，共{}条", shenhuaData.size());
        
        // 爬取CCI指数数据
        List<CoalIndexDataDto> cciData = crawlCoalIndexData(IndexType.CCI);
        resultMap.put(IndexType.CCI, cciData);
        logger.info("CCI指数数据爬取完成，共{}条", cciData.size());
        
        // 爬取CCTD指数数据
        List<CoalIndexDataDto> cctdData = crawlCoalIndexData(IndexType.CCTD);
        resultMap.put(IndexType.CCTD, cctdData);
        logger.info("CCTD指数数据爬取完成，共{}条", cctdData.size());
        
        logger.info("所有煤价指数数据爬取完成");
        return resultMap;
    }
    
    /**
     * 爬取指定类型的煤价指数数据
     * @param indexType 指数类型
     * @return 煤价数据列表
     */
    public static List<CoalIndexDataDto> crawlCoalIndexData(IndexType indexType) {
        List<CoalIndexDataDto> resultList = new ArrayList<>();
        
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(
                new BrowserType.LaunchOptions()
                    .setHeadless(true)
                    .setSlowMo(1000) // 减慢操作速度，避免被检测
            );
            
            BrowserContext context = browser.newContext(
                new Browser.NewContextOptions()
                    .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                    .setViewportSize(1920, 1080)
            );
            
            Page page = context.newPage();
            
            // 设置网络拦截器
            setupNetworkInterceptor(page, resultList, indexType);
            
            // 设置页面超时
            page.setDefaultTimeout(30000);
            
            logger.info("正在访问煤易宝网站: {}", BASE_URL);
            
            // 导航到目标页面
            page.navigate(BASE_URL, new Page.NavigateOptions()
                .setWaitUntil(WaitUntilState.NETWORKIDLE)
                .setTimeout(30000));
            
            logger.info("页面加载完成，开始切换到{}标签页", indexType.getName());
            
            // 等待页面完全加载
            page.waitForTimeout(5000);

            // 截图调试
            page.screenshot(new Page.ScreenshotOptions().setPath(Paths.get("debug_before_click.png")));

            // 点击对应的指数标签页
            clickIndexTab(page, indexType);

            // 等待图表数据加载
            page.waitForTimeout(8000);

            // 截图调试
            page.screenshot(new Page.ScreenshotOptions().setPath(Paths.get("debug_after_click_" + indexType.name().toLowerCase() + ".png")));
            
            // 多种方式提取数据
            extractDataFromMultipleSources(page, indexType, resultList);
            
            // 数据清洗和去重
            resultList = cleanAndDeduplicateData(resultList);
            
            page.close();
            context.close();
            browser.close();
            
        } catch (Exception e) {
            logger.error("爬取{}数据失败: {}", indexType.getName(), e.getMessage(), e);
        }
        
        logger.info("{}数据爬取完成，共获取{}条有效数据", indexType.getName(), resultList.size());
        return resultList;
    }
    
    /**
     * 设置网络拦截器，捕获AJAX请求中的图表数据
     */
    private static void setupNetworkInterceptor(Page page, List<CoalIndexDataDto> resultList, IndexType indexType) {
        page.onResponse(response -> {
            try {
                String url = response.url();
                String contentType = response.headers().get("content-type");
                
                // 过滤相关的数据请求
                if (isRelevantDataRequest(url, contentType)) {
                    String responseBody = response.text();
                    if (responseBody != null && containsChartData(responseBody)) {
                        logger.info("捕获到图表数据响应: {}", url);
                        List<CoalIndexDataDto> networkData = parseNetworkResponse(responseBody, indexType);
                        resultList.addAll(networkData);
                        logger.info("从网络响应中解析出{}条数据", networkData.size());
                    }
                }
            } catch (Exception e) {
                logger.debug("处理网络响应失败: {}", e.getMessage());
            }
        });
    }
    
    /**
     * 判断是否是相关的数据请求
     */
    private static boolean isRelevantDataRequest(String url, String contentType) {
        if (url == null) return false;
        
        String lowerUrl = url.toLowerCase();
        
        // 排除静态资源
        if (lowerUrl.contains(".css") || lowerUrl.contains(".js") || 
            lowerUrl.contains(".png") || lowerUrl.contains(".jpg") ||
            lowerUrl.contains("static") || lowerUrl.contains("font")) {
            return false;
        }
        
        // 关注可能包含图表数据的请求
        boolean isRelevantUrl = lowerUrl.contains("meiyibao.com") &&
                               (lowerUrl.contains("api") || lowerUrl.contains("data") ||
                                lowerUrl.contains("chart") || lowerUrl.contains("ajax") ||
                                lowerUrl.contains("coal") || lowerUrl.contains("price"));
        
        boolean isJsonContent = contentType != null && contentType.contains("json");
        
        return isRelevantUrl || (isJsonContent && lowerUrl.contains("meiyibao.com"));
    }
    
    /**
     * 检查响应内容是否包含图表数据
     */
    private static boolean containsChartData(String content) {
        if (content == null || content.length() < 10) return false;
        
        String lower = content.toLowerCase();
        
        // 检查是否包含煤炭相关关键词和数据格式
        boolean hasCoalKeywords = lower.contains("coal") || lower.contains("煤") ||
                                 lower.contains("price") || lower.contains("价格") ||
                                 lower.contains("cctd") || lower.contains("cci") ||
                                 lower.contains("神华");
        
        // 检查是否包含图表数据格式
        boolean hasChartFormat = content.contains("07-") || // 日期格式
                                content.contains("5500") || content.contains("5000") || content.contains("4500") || // 热值
                                content.matches(".*\\[.*\\d{3,}.*\\].*"); // 数组格式的数字
        
        return hasCoalKeywords && hasChartFormat;
    }
    
    /**
     * 解析网络响应中的图表数据
     */
    private static List<CoalIndexDataDto> parseNetworkResponse(String responseBody, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();
        
        try {
            // 模式1: 查找时间序列数组格式 ["07-24", 648]
            Pattern timeSeriesPattern = Pattern.compile("\\[\"(\\d{2}-\\d{2})\",\\s*(\\d{3,})\\]");
            Matcher matcher = timeSeriesPattern.matcher(responseBody);
            
            while (matcher.find()) {
                String dateStr = matcher.group(1);
                String priceStr = matcher.group(2);
                
                if (isValidDate(dateStr) && isValidPrice(priceStr)) {
                    CoalIndexDataDto data = createCoalIndexData(indexType, dateStr, priceStr, null);
                    if (data != null) {
                        dataList.add(data);
                        logger.debug("解析时间序列数据: {} = {}元", dateStr, priceStr);
                    }
                }
            }
            
            // 模式2: 查找键值对格式 "07-24": 648
            Pattern keyValuePattern = Pattern.compile("\"(\\d{2}-\\d{2})\"\\s*:\\s*(\\d{3,})");
            Matcher kvMatcher = keyValuePattern.matcher(responseBody);
            
            while (kvMatcher.find()) {
                String dateStr = kvMatcher.group(1);
                String priceStr = kvMatcher.group(2);
                
                if (isValidDate(dateStr) && isValidPrice(priceStr)) {
                    CoalIndexDataDto data = createCoalIndexData(indexType, dateStr, priceStr, null);
                    if (data != null) {
                        dataList.add(data);
                        logger.debug("解析键值对数据: {} = {}元", dateStr, priceStr);
                    }
                }
            }
            
            // 模式3: 查找包含热值的完整数据格式
            Pattern fullDataPattern = Pattern.compile("(\\d{4,5})kCal[^\\d]*(\\d{3,})元");
            Matcher fullMatcher = fullDataPattern.matcher(responseBody);
            
            while (fullMatcher.find()) {
                String calorificStr = fullMatcher.group(1);
                String priceStr = fullMatcher.group(2);
                
                if (isValidPrice(priceStr)) {
                    CoalIndexDataDto data = createCoalIndexData(indexType, getCurrentDateString(), priceStr, calorificStr);
                    if (data != null) {
                        dataList.add(data);
                        logger.debug("解析完整数据: {}kCal = {}元", calorificStr, priceStr);
                    }
                }
            }
            
        } catch (Exception e) {
            logger.error("解析网络响应数据失败: {}", e.getMessage(), e);
        }
        
        return dataList;
    }
    
    /**
     * 点击指定的指数标签页
     */
    private static void clickIndexTab(Page page, IndexType indexType) {
        try {
            String tabSelector = getTabSelector(indexType);
            
            // 等待标签页元素出现
            page.waitForSelector(tabSelector, new Page.WaitForSelectorOptions().setTimeout(10000));
            
            // 点击标签页
            page.click(tabSelector);
            
            // 等待标签页切换完成
            page.waitForTimeout(2000);
            
            logger.info("成功切换到{}标签页", indexType.getName());
            
        } catch (Exception e) {
            logger.warn("点击{}标签页失败，使用默认标签页: {}", indexType.getName(), e.getMessage());
        }
    }
    
    /**
     * 获取标签页选择器
     */
    private static String getTabSelector(IndexType indexType) {
        switch (indexType) {
            case SHENHUA:
                return "text=神华外购";
            case CCI:
                return "text=CCI指数";
            case CCTD:
                return "text=CCTD指数";
            default:
                return "text=CCTD指数"; // 默认CCTD
        }
    }

    /**
     * 从多个数据源提取数据
     */
    private static void extractDataFromMultipleSources(Page page, IndexType indexType, List<CoalIndexDataDto> resultList) {
        logger.info("开始从多个数据源提取{}数据", indexType.getName());

        // 1. 从页面文本内容提取
        List<CoalIndexDataDto> textData = extractDataFromPageText(page, indexType);
        resultList.addAll(textData);
        logger.info("从页面文本提取到{}条数据", textData.size());

        // 2. 从DOM元素提取
        List<CoalIndexDataDto> domData = extractDataFromDOM(page, indexType);
        resultList.addAll(domData);
        logger.info("从DOM元素提取到{}条数据", domData.size());

        // 3. 从JavaScript执行结果提取
        List<CoalIndexDataDto> jsData = extractDataFromJavaScript(page, indexType);
        resultList.addAll(jsData);
        logger.info("从JavaScript提取到{}条数据", jsData.size());

        // 4. 从左侧价格卡片提取当前价格
        List<CoalIndexDataDto> cardData = extractDataFromPriceCards(page, indexType);
        resultList.addAll(cardData);
        logger.info("从价格卡片提取到{}条数据", cardData.size());
    }

    /**
     * 从页面文本内容提取数据
     */
    private static List<CoalIndexDataDto> extractDataFromPageText(Page page, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            String pageText = page.textContent("body");
            if (pageText == null || pageText.length() == 0) {
                return dataList;
            }

            // 按行分割文本
            String[] lines = pageText.split("\n");

            for (String line : lines) {
                line = line.trim();
                if (line.length() == 0) continue;

                // 查找包含日期和价格的行
                if (line.contains("07-") || line.contains("08-")) {
                    List<CoalIndexDataDto> lineData = parseLineForCoalData(line, indexType);
                    dataList.addAll(lineData);
                }

                // 查找包含热值和价格的行 (如: 5500kCal 648元)
                if (line.matches(".*\\d{4,5}kCal.*\\d{3,}元.*")) {
                    CoalIndexDataDto data = parseCalorificValueLine(line, indexType);
                    if (data != null) {
                        dataList.add(data);
                    }
                }
            }

        } catch (Exception e) {
            logger.error("从页面文本提取数据失败: {}", e.getMessage(), e);
        }

        return dataList;
    }

    /**
     * 从DOM元素提取数据
     */
    private static List<CoalIndexDataDto> extractDataFromDOM(Page page, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            // 查找图表容器
            String[] chartSelectors = {
                "canvas", "svg", ".chart", ".chart-container",
                ".echarts", "[id*='chart']", "[class*='chart']",
                ".coal-ing", ".price-card", ".index-card"
            };

            for (String selector : chartSelectors) {
                try {
                    Locator elements = page.locator(selector);
                    int count = elements.count();

                    if (count > 0) {
                        logger.debug("选择器{}找到{}个元素", selector, count);

                        for (int i = 0; i < Math.min(count, 5); i++) {
                            try {
                                String text = elements.nth(i).textContent();
                                String html = elements.nth(i).innerHTML();

                                // 分析元素内容
                                List<CoalIndexDataDto> elementData = analyzeElementContent(text, html, indexType);
                                dataList.addAll(elementData);

                            } catch (Exception e) {
                                logger.debug("分析第{}个元素失败: {}", i, e.getMessage());
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.debug("选择器{}执行失败: {}", selector, e.getMessage());
                }
            }

        } catch (Exception e) {
            logger.error("从DOM元素提取数据失败: {}", e.getMessage(), e);
        }

        return dataList;
    }

    /**
     * 从JavaScript执行结果提取数据
     */
    private static List<CoalIndexDataDto> extractDataFromJavaScript(Page page, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            // 简化的JavaScript代码：获取页面文本内容
            String jsCode = "() => { try { return document.body.innerText || ''; } catch(e) { return ''; } }";

            Object jsResult = page.evaluate(jsCode);
            if (jsResult != null) {
                String resultStr = jsResult.toString();
                if (!resultStr.equals("null") && resultStr.length() > 0) {
                    // 解析JavaScript返回的数据
                    List<CoalIndexDataDto> jsData = parseJavaScriptResult(resultStr, indexType);
                    dataList.addAll(jsData);
                }
            }

        } catch (Exception e) {
            logger.error("从JavaScript提取数据失败: {}", e.getMessage(), e);
        }

        return dataList;
    }

    /**
     * 从价格卡片提取当前价格数据
     */
    private static List<CoalIndexDataDto> extractDataFromPriceCards(Page page, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            // 查找价格卡片选择器
            String[] cardSelectors = {
                ".coal-ing", ".price-card", ".index-card",
                "[class*='price']", "[class*='coal']", "[class*='index']"
            };

            for (String selector : cardSelectors) {
                try {
                    Locator cards = page.locator(selector);
                    int count = cards.count();

                    for (int i = 0; i < count; i++) {
                        try {
                            String cardText = cards.nth(i).textContent();
                            if (cardText != null && cardText.trim().length() > 0) {
                                // 解析卡片中的价格和热值信息
                                CoalIndexDataDto cardData = parseCardData(cardText, indexType);
                                if (cardData != null) {
                                    dataList.add(cardData);
                                }
                            }
                        } catch (Exception e) {
                            logger.debug("解析第{}个卡片失败: {}", i, e.getMessage());
                        }
                    }
                } catch (Exception e) {
                    logger.debug("选择器{}执行失败: {}", selector, e.getMessage());
                }
            }

        } catch (Exception e) {
            logger.error("从价格卡片提取数据失败: {}", e.getMessage(), e);
        }

        return dataList;
    }

    /**
     * 解析单行数据，查找日期和价格
     */
    private static List<CoalIndexDataDto> parseLineForCoalData(String line, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            // 查找日期模式 (07-24, 08-01等)
            Pattern datePattern = Pattern.compile("(\\d{2}-\\d{2})");
            Matcher dateMatcher = datePattern.matcher(line);

            if (dateMatcher.find()) {
                String dateStr = dateMatcher.group(1);

                // 在同一行中查找价格
                Pattern pricePattern = Pattern.compile("\\b(\\d{3,})\\b");
                Matcher priceMatcher = pricePattern.matcher(line);

                while (priceMatcher.find()) {
                    String priceStr = priceMatcher.group(1);

                    if (isValidPrice(priceStr)) {
                        CoalIndexDataDto data = createCoalIndexData(indexType, dateStr, priceStr, null);
                        if (data != null) {
                            dataList.add(data);
                            logger.debug("从行中解析: {} = {}元", dateStr, priceStr);
                        }
                    }
                }
            }

        } catch (Exception e) {
            logger.debug("解析行数据失败: {}", line, e);
        }

        return dataList;
    }

    /**
     * 解析包含热值的行数据
     */
    private static CoalIndexDataDto parseCalorificValueLine(String line, IndexType indexType) {
        try {
            // 匹配格式: 5500kCal 648元 或 648元 5500kCal
            Pattern pattern1 = Pattern.compile("(\\d{4,5})kCal[^\\d]*(\\d{3,})元");
            Pattern pattern2 = Pattern.compile("(\\d{3,})元[^\\d]*(\\d{4,5})kCal");

            Matcher matcher1 = pattern1.matcher(line);
            Matcher matcher2 = pattern2.matcher(line);

            String calorificStr = null;
            String priceStr = null;

            if (matcher1.find()) {
                calorificStr = matcher1.group(1);
                priceStr = matcher1.group(2);
            } else if (matcher2.find()) {
                priceStr = matcher2.group(1);
                calorificStr = matcher2.group(2);
            }

            if (calorificStr != null && priceStr != null && isValidPrice(priceStr)) {
                return createCoalIndexData(indexType, getCurrentDateString(), priceStr, calorificStr);
            }

        } catch (Exception e) {
            logger.debug("解析热值行失败: {}", line, e);
        }

        return null;
    }

    /**
     * 分析元素内容
     */
    private static List<CoalIndexDataDto> analyzeElementContent(String text, String html, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            if (text != null && text.length() > 0) {
                // 从文本中查找数据
                if (text.contains("07-") || text.contains("08-") || text.contains("kCal")) {
                    dataList.addAll(parseLineForCoalData(text, indexType));

                    CoalIndexDataDto calorificData = parseCalorificValueLine(text, indexType);
                    if (calorificData != null) {
                        dataList.add(calorificData);
                    }
                }
            }

            if (html != null && html.length() > 0) {
                // 从HTML中查找数据
                dataList.addAll(parseHTMLContent(html, indexType));
            }

        } catch (Exception e) {
            logger.debug("分析元素内容失败: {}", e.getMessage());
        }

        return dataList;
    }

    /**
     * 解析HTML内容
     */
    private static List<CoalIndexDataDto> parseHTMLContent(String html, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            // 查找时间序列数据
            Pattern timeSeriesPattern = Pattern.compile("\\[\"(\\d{2}-\\d{2})\",\\s*(\\d{3,})\\]");
            Matcher matcher = timeSeriesPattern.matcher(html);

            while (matcher.find()) {
                String dateStr = matcher.group(1);
                String priceStr = matcher.group(2);

                if (isValidDate(dateStr) && isValidPrice(priceStr)) {
                    CoalIndexDataDto data = createCoalIndexData(indexType, dateStr, priceStr, null);
                    if (data != null) {
                        dataList.add(data);
                    }
                }
            }

        } catch (Exception e) {
            logger.debug("解析HTML内容失败: {}", e.getMessage());
        }

        return dataList;
    }

    /**
     * 解析JavaScript执行结果
     */
    private static List<CoalIndexDataDto> parseJavaScriptResult(String jsResult, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            // 从JavaScript结果中提取时间序列数据
            Pattern timeSeriesPattern = Pattern.compile("(\\d{2}-\\d{2})[^\\d]*(\\d{3,})");
            Matcher matcher = timeSeriesPattern.matcher(jsResult);

            while (matcher.find()) {
                String dateStr = matcher.group(1);
                String priceStr = matcher.group(2);

                if (isValidDate(dateStr) && isValidPrice(priceStr)) {
                    CoalIndexDataDto data = createCoalIndexData(indexType, dateStr, priceStr, null);
                    if (data != null) {
                        dataList.add(data);
                    }
                }
            }

        } catch (Exception e) {
            logger.debug("解析JavaScript结果失败: {}", e.getMessage());
        }

        return dataList;
    }

    /**
     * 解析卡片数据
     */
    private static CoalIndexDataDto parseCardData(String cardText, IndexType indexType) {
        try {
            // 查找价格和热值信息
            Pattern pricePattern = Pattern.compile("(\\d{3,})元");
            Pattern calorificPattern = Pattern.compile("(\\d{4,5})kCal");

            Matcher priceMatcher = pricePattern.matcher(cardText);
            Matcher calorificMatcher = calorificPattern.matcher(cardText);

            if (priceMatcher.find()) {
                String priceStr = priceMatcher.group(1);
                String calorificStr = null;

                if (calorificMatcher.find()) {
                    calorificStr = calorificMatcher.group(1);
                }

                if (isValidPrice(priceStr)) {
                    return createCoalIndexData(indexType, getCurrentDateString(), priceStr, calorificStr);
                }
            }

        } catch (Exception e) {
            logger.debug("解析卡片数据失败: {}", cardText, e);
        }

        return null;
    }

    /**
     * 创建煤炭指数数据对象
     */
    private static CoalIndexDataDto createCoalIndexData(IndexType indexType, String dateStr, String priceStr, String calorificStr) {
        try {
            CoalIndexDataDto data = new CoalIndexDataDto();
            data.setIndexType(indexType);
            data.setPrice(new BigDecimal(priceStr));
            data.setDataDate(parseDate(dateStr));
            data.setSourceUrl(BASE_URL);

            if (calorificStr != null) {
                data.setCalorificValue(Integer.parseInt(calorificStr));
            }

            return data;

        } catch (Exception e) {
            logger.debug("创建数据对象失败: date={}, price={}, calorific={}", dateStr, priceStr, calorificStr);
            return null;
        }
    }

    /**
     * 数据清洗和去重
     */
    private static List<CoalIndexDataDto> cleanAndDeduplicateData(List<CoalIndexDataDto> dataList) {
        List<CoalIndexDataDto> cleanedList = new ArrayList<>();
        Set<String> seenData = new HashSet<>();

        for (CoalIndexDataDto data : dataList) {
            try {
                // 数据有效性检查
                if (data.getPrice() == null) continue;

                BigDecimal price = data.getPrice();

                // 价格范围检查
                if (price.compareTo(MIN_PRICE) < 0 || price.compareTo(MAX_PRICE) > 0) {
                    continue;
                }

                // 创建唯一标识符
                String uniqueKey = data.getIndexType() + "_" +
                                  (data.getDataDate() != null ? DATE_FORMAT.format(data.getDataDate()) : "current") + "_" +
                                  price.toString() + "_" +
                                  (data.getCalorificValue() != null ? data.getCalorificValue().toString() : "");

                if (!seenData.contains(uniqueKey)) {
                    seenData.add(uniqueKey);
                    cleanedList.add(data);
                }

            } catch (Exception e) {
                logger.debug("清洗数据时出错: {}", e.getMessage());
            }
        }

        // 按日期排序
        cleanedList.sort((a, b) -> {
            if (a.getDataDate() == null && b.getDataDate() == null) return 0;
            if (a.getDataDate() == null) return 1;
            if (b.getDataDate() == null) return -1;
            return a.getDataDate().compareTo(b.getDataDate());
        });

        return cleanedList;
    }

    /**
     * 验证日期格式
     */
    private static boolean isValidDate(String dateStr) {
        if (dateStr == null || dateStr.length() != 5) return false;
        return dateStr.matches("\\d{2}-\\d{2}");
    }

    /**
     * 验证价格范围
     */
    private static boolean isValidPrice(String priceStr) {
        try {
            BigDecimal price = new BigDecimal(priceStr);
            return price.compareTo(MIN_PRICE) >= 0 && price.compareTo(MAX_PRICE) <= 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 解析日期字符串
     */
    private static Date parseDate(String dateStr) {
        try {
            if (dateStr == null || dateStr.length() != 5) {
                return new Date(); // 返回当前日期
            }

            String[] parts = dateStr.split("-");
            if (parts.length == 2) {
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.MONTH, Integer.parseInt(parts[0]) - 1);
                calendar.set(Calendar.DAY_OF_MONTH, Integer.parseInt(parts[1]));
                return calendar.getTime();
            }
        } catch (Exception e) {
            logger.debug("解析日期失败: {}", dateStr);
        }
        return new Date();
    }

    /**
     * 获取当前日期字符串
     */
    private static String getCurrentDateString() {
        return DATE_FORMAT.format(new Date());
    }

    /**
     * 格式化输出数据
     */
    public static void printFormattedData(Map<IndexType, List<CoalIndexDataDto>> dataMap) {
        System.out.println("\n=== 煤易宝煤价指数数据爬取结果 ===");

        for (Map.Entry<IndexType, List<CoalIndexDataDto>> entry : dataMap.entrySet()) {
            IndexType indexType = entry.getKey();
            List<CoalIndexDataDto> dataList = entry.getValue();

            System.out.println("\n--- " + indexType.getName() + " ---");
            System.out.println("总条数: " + dataList.size());

            if (dataList.isEmpty()) {
                System.out.println("未获取到有效数据");
                continue;
            }

            // 按日期分组显示
            Map<String, List<CoalIndexDataDto>> groupedByDate = new LinkedHashMap<>();

            for (CoalIndexDataDto data : dataList) {
                String dateKey = data.getDataDate() != null ?
                    DATE_FORMAT.format(data.getDataDate()) : "当前";

                groupedByDate.computeIfAbsent(dateKey, k -> new ArrayList<>()).add(data);
            }

            // 输出格式化结果
            for (Map.Entry<String, List<CoalIndexDataDto>> dateEntry : groupedByDate.entrySet()) {
                String date = dateEntry.getKey();
                List<CoalIndexDataDto> dayData = dateEntry.getValue();

                System.out.print(date + ": ");

                List<String> priceInfo = new ArrayList<>();
                for (CoalIndexDataDto data : dayData) {
                    String info = "";
                    if (data.getCalorificValue() != null) {
                        info = data.getCalorificValue() + "kCal=" + data.getPrice() + "元";
                    } else {
                        info = data.getPrice() + "元";
                    }
                    priceInfo.add(info);
                }

                System.out.println(String.join(", ", priceInfo));
            }
        }
    }

    /**
     * 主方法 - 用于测试
     */
    public static void main(String[] args) {
        System.out.println("=== 煤易宝煤价指数数据爬取工具 ===");
        System.out.println("目标：爬取神华外购、CCI指数、CCTD指数的折线图数据");
        System.out.println("数据格式：日期对应的大卡数和价格，如 07-24: 5500kCal=648元, 5000kCal=581元, 4500kCal=517元");

        try {
            // 测试：只爬取CCTD指数数据
            Map<IndexType, List<CoalIndexDataDto>> allData = new HashMap<>();
            List<CoalIndexDataDto> cctdData = crawlCoalIndexData(IndexType.CCTD);
            allData.put(IndexType.CCTD, cctdData);

            // 格式化输出结果
            printFormattedData(allData);

            // 统计信息
            int totalCount = allData.values().stream().mapToInt(List::size).sum();
            System.out.println("\n=== 爬取完成 ===");
            System.out.println("总共获取到 " + totalCount + " 条有效数据");

            if (totalCount == 0) {
                System.out.println("\n可能的原因：");
                System.out.println("1. 网络连接问题");
                System.out.println("2. 网站结构发生变化");
                System.out.println("3. 页面加载时间不足");
                System.out.println("4. 反爬虫机制");
            }

        } catch (Exception e) {
            System.err.println("爬取过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
