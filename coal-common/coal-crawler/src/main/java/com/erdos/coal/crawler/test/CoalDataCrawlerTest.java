// package com.erdos.coal.crawler.test;
//
// import com.erdos.coal.crawler.dto.CoalIndexDataDto;
// import com.erdos.coal.crawler.enums.IndexType;
// import com.erdos.coal.crawler.util.ChartDataExtractor;
// import com.erdos.coal.crawler.util.CrawlerDebugUtil;
//
// import java.text.SimpleDateFormat;
// import java.util.List;
// import java.util.Map;
// import java.util.stream.Collectors;
//
// /**
//  * 煤炭数据爬虫测试类
//  * 用于测试和验证爬虫功能
//  *
//  * <AUTHOR>
//  * @date 2025-07-31
//  */
// public class CoalDataCrawlerTest {
//
//     private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
//
//     public static void main(String[] args) {
//         System.out.println("=== 煤炭数据爬虫测试 ===");
//
//         // 1. 运行调试工具
//         System.out.println("\n1. 运行调试工具分析页面结构...");
//         runDebugTool();
//
//         // 2. 测试CCTD指数数据提取
//         System.out.println("\n2. 测试CCTD指数数据提取...");
//         testCCTDIndexExtraction();
//
//         // 3. 测试CCI指数数据提取
//         System.out.println("\n3. 测试CCI指数数据提取...");
//         testCCIIndexExtraction();
//
//         // 4. 测试神华外购数据提取
//         System.out.println("\n4. 测试神华外购数据提取...");
//         testShenhuaExternalExtraction();
//
//         // 5. 综合分析
//         System.out.println("\n5. 综合分析...");
//         comprehensiveAnalysis();
//
//         System.out.println("\n=== 测试完成 ===");
//     }
//
//     /**
//      * 运行调试工具
//      */
//     private static void runDebugTool() {
//         try {
//             System.out.println("启动页面结构分析工具...");
//             // 注意：这个方法会打开浏览器窗口，需要手动关闭
//             // CrawlerDebugUtil.debugPageElements();
//             System.out.println("调试工具需要手动运行，请执行 CrawlerDebugUtil.main()");
//         } catch (Exception e) {
//             System.err.println("调试工具运行失败: " + e.getMessage());
//         }
//     }
//
//     /**
//      * 测试CCTD指数数据提取
//      */
//     private static void testCCTDIndexExtraction() {
//         try {
//             System.out.println("正在提取CCTD指数数据...");
//
//             List<CoalIndexDataDto> cctdData = ChartDataExtractor.extractChartData(IndexType.CCTD);
//
//             System.out.println("CCTD指数数据提取结果:");
//             System.out.println("- 总条数: " + cctdData.size());
//
//             if (!cctdData.isEmpty()) {
//                 // 按热值分组统计
//                 Map<Integer, List<CoalIndexDataDto>> groupedByCalorific = cctdData.stream()
//                     .filter(data -> data.getCalorificValue() != null)
//                     .collect(Collectors.groupingBy(CoalIndexDataDto::getCalorificValue));
//
//                 System.out.println("- 按热值分组:");
//                 groupedByCalorific.forEach((calorific, dataList) -> {
//                     System.out.println("  " + calorific + "kCal: " + dataList.size() + "条");
//                     if (!dataList.isEmpty()) {
//                         CoalIndexDataDto sample = dataList.get(0);
//                         System.out.println("    示例价格: " + sample.getPrice() + "元");
//                     }
//                 });
//
//                 // 显示最新的几条数据
//                 System.out.println("- 最新数据样例:");
//                 cctdData.stream().limit(5).forEach(data -> {
//                     System.out.println("  价格: " + data.getPrice() + "元, " +
//                                      "热值: " + data.getCalorificValue() + "kCal, " +
//                                      "日期: " + DATE_FORMAT.format(data.getDataDate()));
//                 });
//             } else {
//                 System.out.println("⚠ 未提取到CCTD指数数据");
//             }
//
//         } catch (Exception e) {
//             System.err.println("CCTD指数数据提取失败: " + e.getMessage());
//             e.printStackTrace();
//         }
//     }
//
//     /**
//      * 测试CCI指数数据提取
//      */
//     private static void testCCIIndexExtraction() {
//         try {
//             System.out.println("正在提取CCI指数数据...");
//
//             List<CoalIndexDataDto> cciData = ChartDataExtractor.extractChartData(IndexType.CCI);
//
//             System.out.println("CCI指数数据提取结果:");
//             System.out.println("- 总条数: " + cciData.size());
//
//             if (!cciData.isEmpty()) {
//                 // 价格统计
//                 double avgPrice = cciData.stream()
//                     .filter(data -> data.getPrice() != null)
//                     .mapToDouble(data -> data.getPrice().doubleValue())
//                     .average()
//                     .orElse(0.0);
//
//                 System.out.println("- 平均价格: " + String.format("%.2f", avgPrice) + "元");
//
//                 // 显示数据样例
//                 System.out.println("- 数据样例:");
//                 cciData.stream().limit(3).forEach(data -> {
//                     System.out.println("  价格: " + data.getPrice() + "元, " +
//                                      "日期: " + DATE_FORMAT.format(data.getDataDate()));
//                 });
//             } else {
//                 System.out.println("⚠ 未提取到CCI指数数据");
//             }
//
//         } catch (Exception e) {
//             System.err.println("CCI指数数据提取失败: " + e.getMessage());
//             e.printStackTrace();
//         }
//     }
//
//     /**
//      * 测试神华外购数据提取
//      */
//     private static void testShenhuaExternalExtraction() {
//         try {
//             System.out.println("正在提取神华外购数据...");
//
//             List<CoalIndexDataDto> shenhuaData = ChartDataExtractor.extractChartData(IndexType.SHENHUA);
//
//             System.out.println("神华外购数据提取结果:");
//             System.out.println("- 总条数: " + shenhuaData.size());
//
//             if (!shenhuaData.isEmpty()) {
//                 // 显示数据样例
//                 System.out.println("- 数据样例:");
//                 shenhuaData.stream().limit(3).forEach(data -> {
//                     System.out.println("  价格: " + data.getPrice() + "元, " +
//                                      "热值: " + data.getCalorificValue() + "kCal, " +
//                                      "日期: " + DATE_FORMAT.format(data.getDataDate()));
//                 });
//             } else {
//                 System.out.println("⚠ 未提取到神华外购数据");
//             }
//
//         } catch (Exception e) {
//             System.err.println("神华外购数据提取失败: " + e.getMessage());
//             e.printStackTrace();
//         }
//     }
//
//     /**
//      * 综合分析
//      */
//     private static void comprehensiveAnalysis() {
//         try {
//             System.out.println("正在进行综合分析...");
//
//             // 提取所有类型的数据
//             List<CoalIndexDataDto> allData = extractAllData();
//
//             if (allData.isEmpty()) {
//                 System.out.println("⚠ 未提取到任何数据，可能的原因:");
//                 System.out.println("  1. 网络连接问题");
//                 System.out.println("  2. 网站结构发生变化");
//                 System.out.println("  3. JavaScript执行失败");
//                 System.out.println("  4. 选择器不匹配");
//
//                 System.out.println("\n建议解决方案:");
//                 System.out.println("  1. 检查网络连接");
//                 System.out.println("  2. 运行CrawlerDebugUtil.debugPageElements()分析页面结构");
//                 System.out.println("  3. 更新选择器和JavaScript代码");
//                 System.out.println("  4. 检查网站是否有反爬虫机制");
//
//                 return;
//             }
//
//             // 按指数类型分组
//             Map<IndexType, List<CoalIndexDataDto>> groupedByType = allData.stream()
//                 .collect(Collectors.groupingBy(CoalIndexDataDto::getIndexType));
//
//             System.out.println("数据提取成功统计:");
//             groupedByType.forEach((type, dataList) -> {
//                 System.out.println("- " + getIndexTypeName(type) + ": " + dataList.size() + "条");
//             });
//
//             // 数据质量分析
//             long validPriceCount = allData.stream()
//                 .filter(data -> data.getPrice() != null && data.getPrice().doubleValue() > 0)
//                 .count();
//
//             long validCalorificCount = allData.stream()
//                 .filter(data -> data.getCalorificValue() != null && data.getCalorificValue() > 0)
//                 .count();
//
//             System.out.println("\n数据质量分析:");
//             System.out.println("- 有效价格数据: " + validPriceCount + "/" + allData.size());
//             System.out.println("- 有效热值数据: " + validCalorificCount + "/" + allData.size());
//
//             // 时间范围分析
//             if (validPriceCount > 0) {
//                 System.out.println("\n✓ 数据提取成功！建议进一步优化:");
//                 System.out.println("  1. 增加数据验证逻辑");
//                 System.out.println("  2. 添加重复数据去除");
//                 System.out.println("  3. 完善日期解析");
//                 System.out.println("  4. 添加数据持久化");
//             }
//
//         } catch (Exception e) {
//             System.err.println("综合分析失败: " + e.getMessage());
//             e.printStackTrace();
//         }
//     }
//
//     /**
//      * 提取所有类型的数据
//      */
//     private static List<CoalIndexDataDto> extractAllData() {
//         List<CoalIndexDataDto> allData = new java.util.ArrayList<>();
//
//         try {
//             allData.addAll(ChartDataExtractor.extractChartData(IndexType.CCTD));
//             allData.addAll(ChartDataExtractor.extractChartData(IndexType.CCI));
//             allData.addAll(ChartDataExtractor.extractChartData(IndexType.SHENHUA));
//         } catch (Exception e) {
//             System.err.println("提取数据失败: " + e.getMessage());
//         }
//
//         return allData;
//     }
//
//     /**
//      * 获取指数类型名称
//      */
//     private static String getIndexTypeName(IndexType type) {
//         switch (type) {
//             case CCTD:
//                 return "CCTD指数";
//             case CCI:
//                 return "CCI指数";
//             case SHENHUA:
//                 return "神华外购";
//             default:
//                 return type.toString();
//         }
//     }
// }
