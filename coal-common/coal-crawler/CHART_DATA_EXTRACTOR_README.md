# ChartDataExtractor - 煤易宝网站数据提取工具

## 功能概述

ChartDataExtractor是专门用于从煤易宝网站(https://www.meiyibao.com/)提取煤价指数数据的工具类。支持三种数据类型的提取：

1. **神华外购指数** - 包含外购5500kCal、外购5000kCal、外购4500kCal、外购神优2等数据
2. **CCI指数** - 包含5500kCal、5000kCal、4500kCal等数据
3. **CCTD指数** - 包含5500kCal、5000kCal、4500kCal等数据

## 实现完成状态 ✅

**已完成的功能：**
- ✅ 完整的ChartDataExtractor类实现
- ✅ Canvas折线图交互和鼠标采样
- ✅ 三种指数类型的标签页切换
- ✅ 数据去重和验证机制
- ✅ 多种数据格式解析支持
- ✅ 兼容性方法和测试类
- ✅ 编译通过，无语法错误

## 主要特性

### 1. Canvas折线图交互
- 自动定位 `class="coal-char"` 元素中的canvas
- 模拟鼠标在折线图上的移动和点击
- 通过30个采样点覆盖所有日期数据点
- 多层Y坐标采样（30%, 50%, 70%）以捕获不同折线数据
- 提取tooltip中的动态数据

### 2. 智能标签页切换
- 自动识别并点击对应的指数标签页
- 支持多种选择器策略，提高成功率
- 神华外购、CCI指数、CCTD指数自动切换
- 容错机制，选择器失败时使用备用方案

### 3. 多层数据提取策略
1. **Canvas交互提取** - 主要方法，通过鼠标交互获取动态数据
2. **页面文本提取** - 备用方法，从页面文本中提取数据
3. **数据去重机制** - 避免重复数据，确保数据质量
4. **数据验证** - 价格范围验证（200-1000元），热值验证

### 4. 数据格式支持
- **神华数据格式**: `07-15: 外购5500kCal=423元, 外购5000kCal=368元, 外购4500kCal=298元，外购神优2=474元`
- **CCI数据格式**: `07-24: 5500kCal=648元, 5000kCal=581元, 4500kCal=517元`
- **CCTD数据格式**: `07-24: 5500kCal=648元, 5000kCal=584元, 4500kCal=518元`

## 核心方法说明

### `extractMeiyibaoData(IndexType indexType)`
- 主要的数据提取方法
- 参数：IndexType.SHENHUA / IndexType.CCI / IndexType.CCTD
- 返回：Map<String, Map<String, String>> 格式的数据
- 自动处理浏览器启动、页面导航、标签页切换

### `extractChartData(IndexType indexType)`
- 兼容测试类的方法
- 返回：List<CoalIndexDataDto> 格式的数据
- 自动转换数据格式，包括日期解析和热值转换

### `extractDataFromCanvas(Page page)`
- Canvas折线图数据提取核心方法
- 查找coal-char类和canvas元素
- 获取canvas边界框并计算采样区域
- 等待图表加载完成后开始交互

### `extractDataByMouseInteraction(Page page, Locator canvas, BoundingBox bounds)`
- 执行具体的鼠标交互操作
- 在折线图上进行30个采样点的密集采样
- 在多个Y坐标位置（30%, 50%, 70%）采样以捕获不同折线数据
- 每个采样点等待300ms，点击后等待500ms确保数据更新

### `clickIndexTab(Page page, IndexType indexType)`
- 智能标签页切换方法
- 支持多种选择器策略：文本匹配、ID匹配、类名匹配等
- 自动容错，失败时尝试备用选择器

### `DataDeduplicator内部类`
- 数据去重和验证辅助类
- 基于原始数据字符串去重，避免重复处理
- 价格范围验证（200-1000元）
- 热值类型验证（5500kCal、5000kCal、4500kCal、神优2）

## 使用方法

### 1. 基本使用
```java
// 提取神华外购数据
Map<String, Map<String, String>> shenhuaData = ChartDataExtractor.extractMeiyibaoData(IndexType.SHENHUA);
// 输出格式：{"07-15": {"外购5500kCal": "423元", "外购5000kCal": "368元", "外购4500kCal": "298元", "外购神优2": "474元"}}

// 提取CCI指数数据
Map<String, Map<String, String>> cciData = ChartDataExtractor.extractMeiyibaoData(IndexType.CCI);
// 输出格式：{"07-24": {"5500kCal": "648元", "5000kCal": "581元", "4500kCal": "517元"}}

// 提取CCTD指数数据
Map<String, Map<String, String>> cctdData = ChartDataExtractor.extractMeiyibaoData(IndexType.CCTD);
// 输出格式：{"07-24": {"5500kCal": "648元", "5000kCal": "584元", "4500kCal": "518元"}}
```

### 2. 兼容测试类使用
```java
// 返回CoalIndexDataDto列表格式，便于与现有系统集成
List<CoalIndexDataDto> shenhuaData = ChartDataExtractor.extractChartData(IndexType.SHENHUA);
List<CoalIndexDataDto> cciData = ChartDataExtractor.extractChartData(IndexType.CCI);
List<CoalIndexDataDto> cctdData = ChartDataExtractor.extractChartData(IndexType.CCTD);

// 每个DTO包含：indexType, dataDate, calorificValue, price, sourceUrl
```

### 3. 直接运行测试
```bash
# 编译项目
cd coal-common/coal-crawler
mvn compile

# 运行主方法进行完整测试
java -cp "target/classes;target/dependency/*" com.erdos.coal.crawler.util.ChartDataExtractor

# 或运行专门的测试类
java -cp "target/classes;target/dependency/*" com.erdos.coal.crawler.test.ChartDataExtractorTest

# 或使用提供的批处理脚本（Windows）
run_chart_extractor.bat
```

## 配置说明

### 浏览器配置
- 使用Chromium浏览器（通过Playwright）
- 非无头模式（便于调试，可修改为无头模式）
- 2分钟超时设置（120000ms）
- 1920x1080分辨率
- 自定义User-Agent，模拟真实浏览器

### 采样配置
- X轴采样点数：30个（密集采样，覆盖整个时间轴）
- Y轴采样位置：30%, 50%, 70%（多层采样，捕获不同折线）
- 鼠标移动间隔：300ms（确保tooltip显示）
- 点击等待时间：500ms（确保数据更新）
- Canvas边界计算：左右各留10%边距

### 数据验证
- 价格范围：200-1000元（过滤异常数据）
- 支持的热值：4500kCal, 5000kCal, 5500kCal, 神优2
- 日期格式：MM-dd（如07-15），自动补充当前年份
- 神优2映射：自动转换为5800大卡

### 标签页选择器策略
- 文本匹配：`text=神华外购`、`text=CCI指数`、`text=CCTD指数`
- 属性匹配：`[data-tab='shenhua']`等
- ID匹配：`#tab-1`、`#tab-2`、`#tab-3`
- 类名匹配：`.tab-item:has-text('神华外购')`等

## 数据去重机制

### DataDeduplicator类
- 基于原始数据字符串去重
- 价格有效性验证
- 避免相同日期相同热值的重复数据
- 提供处理统计信息

## 注意事项

1. **网络依赖**：需要稳定的网络连接访问煤易宝网站
2. **浏览器依赖**：需要安装Playwright的Chromium浏览器
3. **动态内容**：网站内容可能动态变化，需要适当调整等待时间
4. **反爬虫**：网站可能有反爬虫机制，建议适当控制访问频率

## 测试

### 编译项目
```bash
cd coal-common/coal-crawler
mvn compile
```

### 运行测试
```bash
# 运行主测试方法
java -cp target/classes com.erdos.coal.crawler.util.ChartDataExtractor

# 运行专门的测试类
java -cp target/classes com.erdos.coal.crawler.test.ChartDataExtractorTest
```

## 故障排除

1. **找不到canvas元素**
   - 检查网站结构是否变化
   - 确认对应标签页是否正确点击
   - 增加等待时间

2. **提取不到数据**
   - 增加采样点数和等待时间
   - 检查tooltip选择器是否正确
   - 查看浏览器控制台错误信息

3. **数据格式异常**
   - 检查正则表达式匹配规则
   - 验证价格范围过滤条件
   - 查看日志输出的调试信息

## 版本历史

- v1.0 - 初始版本，支持三种指数类型的Canvas交互提取
- 基于CCTDExtractor的成熟逻辑进行开发
- 支持数据去重和多种数据格式解析
